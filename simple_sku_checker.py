import pandas as pd
import numpy as np

print("=== SIMPLE SKU VERIFICATION CHECKER ===")
print("Comparing Power BI data with SOT data")
print("=" * 60)

# Load data
pbi_data = pd.read_csv('powerbi_upload_fixed_dates.csv')
sot_36m = pd.read_csv('perfect_reconciliation_36_month.csv')

print(f"📊 DATA LOADED:")
print(f"  Power BI data: {len(pbi_data)} records")
print(f"  SOT 36-month: {len(sot_36m)} records")

# Get unique SKUs from each dataset
pbi_skus = set(pbi_data['SKU'].unique())
sot_skus = set(sot_36m['Material'].astype(str).unique())

print(f"\n📋 SKU COMPARISON:")
print(f"  Power BI unique SKUs: {len(pbi_skus)}")
print(f"  SOT unique SKUs: {len(sot_skus)}")

# Find overlapping SKUs
common_skus = pbi_skus.intersection(sot_skus)
pbi_only_skus = pbi_skus - sot_skus
sot_only_skus = sot_skus - pbi_skus

print(f"  Common SKUs: {len(common_skus)}")
print(f"  PBI only SKUs: {len(pbi_only_skus)}")
print(f"  SOT only SKUs: {len(sot_only_skus)}")

print(f"\n🔍 SAMPLE SKUs FROM EACH DATASET:")
print(f"Power BI SKUs (first 10): {sorted(list(pbi_skus))[:10]}")
print(f"SOT SKUs (first 10): {sorted(list(sot_skus))[:10]}")

if len(common_skus) > 0:
    print(f"\n✅ COMMON SKUs (first 10): {sorted(list(common_skus))[:10]}")
    
    # For common SKUs, show detailed verification
    print(f"\n🔍 DETAILED VERIFICATION FOR COMMON SKUs:")
    print("=" * 60)
    
    for sku in sorted(list(common_skus))[:5]:  # Show first 5 common SKUs
        print(f"\n📊 SKU: {sku}")
        print("-" * 30)
        
        # Power BI data for this SKU
        pbi_sku_data = pbi_data[pbi_data['SKU'] == int(sku)]
        pbi_plants = pbi_sku_data['Plant'].unique()
        
        # SOT data for this SKU
        sot_sku_data = sot_36m[sot_36m['Material'] == sku]
        sot_plants = sot_sku_data['Plant'].unique()
        
        print(f"  Power BI plants: {sorted(pbi_plants)}")
        print(f"  SOT plants: {sorted(sot_plants)}")
        
        # Check each plant
        for plant in sorted(set(pbi_plants).union(set(sot_plants))):
            pbi_plant_data = pbi_sku_data[pbi_sku_data['Plant'] == plant]
            sot_plant_data = sot_sku_data[sot_sku_data['Plant'] == plant]
            
            if not pbi_plant_data.empty and not sot_plant_data.empty:
                pbi_total_stock = pbi_plant_data['Total Stock'].iloc[0]
                sot_closing_stock = sot_plant_data['Closing Stock (36m)'].iloc[0]
                sot_opening = sot_plant_data['Opening Stock (36m)'].iloc[0]
                sot_receipts = sot_plant_data['Total Receipts (36m)'].iloc[0]
                sot_issues = sot_plant_data['Total Issues (36m)'].iloc[0]
                
                # Verify equation
                calculated_closing = sot_opening + sot_receipts + sot_issues
                equation_ok = abs(calculated_closing - sot_closing_stock) < 0.01
                stock_match = abs(pbi_total_stock - sot_closing_stock) < 0.01
                
                print(f"    Plant {plant}:")
                print(f"      SOT: Opening={sot_opening:,.1f} + Receipts={sot_receipts:,.1f} + Issues={sot_issues:,.1f} = {calculated_closing:,.1f}")
                print(f"      SOT Closing: {sot_closing_stock:,.1f} {'✅' if equation_ok else '❌'}")
                print(f"      PBI Total Stock: {pbi_total_stock:,.1f}")
                print(f"      Match: {'✅ YES' if stock_match else f'❌ NO (Diff: {pbi_total_stock - sot_closing_stock:,.1f})'}")
                
                # Show individual PBI transactions
                pbi_transactions = pbi_plant_data[['Entry date', 'Assigned', 'Value Assigned']]
                print(f"      PBI Transactions ({len(pbi_transactions)}):")
                for _, trans in pbi_transactions.iterrows():
                    print(f"        {trans['Entry date']}: Qty={trans['Assigned']:,.1f}, Value={trans['Value Assigned']:,.1f}")
                    
            elif not pbi_plant_data.empty:
                print(f"    Plant {plant}: PBI only (no SOT data)")
            elif not sot_plant_data.empty:
                print(f"    Plant {plant}: SOT only (no PBI data)")

else:
    print(f"\n❌ NO COMMON SKUs FOUND!")
    print("This means Power BI data and SOT data have completely different SKUs")

# Show the issue clearly
print(f"\n🚨 ISSUE ANALYSIS:")
print("=" * 60)

if len(common_skus) == 0:
    print("❌ CRITICAL ISSUE: No overlapping SKUs between Power BI and SOT data")
    print("   This suggests:")
    print("   1. Power BI is using different source data")
    print("   2. SKU formatting is different")
    print("   3. Different time periods or filters")
    
    print(f"\n🔍 SKU FORMAT ANALYSIS:")
    print(f"Power BI SKU examples: {sorted(list(pbi_skus))[:5]}")
    print(f"SOT SKU examples: {sorted(list(sot_skus))[:5]}")
    
    # Check if it's a formatting issue
    pbi_skus_str = set(str(sku) for sku in pbi_data['SKU'].unique())
    sot_skus_str = set(str(sku) for sku in sot_36m['Material'].unique())
    
    if pbi_skus_str.intersection(sot_skus_str):
        print("✅ String conversion reveals some matches - it's a data type issue")
    else:
        print("❌ Even string conversion shows no matches - completely different datasets")

elif len(common_skus) < len(pbi_skus) * 0.5:
    print(f"⚠️ LIMITED OVERLAP: Only {len(common_skus)}/{len(pbi_skus)} SKUs match")
    print("   This suggests partial data alignment issues")

else:
    print(f"✅ GOOD OVERLAP: {len(common_skus)}/{len(pbi_skus)} SKUs match")

# Create a simple comparison file
comparison_data = []

# Add all Power BI SKUs
for sku in pbi_skus:
    pbi_sku_data = pbi_data[pbi_data['SKU'] == int(sku)]
    total_pbi_stock = pbi_sku_data['Total Stock'].sum()
    total_pbi_value = pbi_sku_data['Total Value'].sum()
    plants = ', '.join(sorted(pbi_sku_data['Plant'].unique()))
    
    in_sot = str(sku) in sot_skus
    
    comparison_data.append({
        'SKU': sku,
        'In_PBI': True,
        'In_SOT': in_sot,
        'PBI_Total_Stock': total_pbi_stock,
        'PBI_Total_Value': total_pbi_value,
        'PBI_Plants': plants,
        'Status': 'Common' if in_sot else 'PBI Only'
    })

# Save comparison
comparison_df = pd.DataFrame(comparison_data)
comparison_df.to_csv('sku_comparison_report.csv', index=False)

print(f"\n💾 COMPARISON REPORT SAVED: sku_comparison_report.csv")
print(f"   This shows which SKUs are in PBI vs SOT data")

print(f"\n🎯 NEXT STEPS:")
if len(common_skus) == 0:
    print("1. Investigate why Power BI and SOT have different SKUs")
    print("2. Check if Power BI is using the right source data")
    print("3. Verify SKU formatting and data types")
    print("4. Confirm time periods and filters match")
else:
    print("1. Focus verification on the common SKUs")
    print("2. Investigate why some SKUs are missing from each dataset")
    print("3. Use the detailed verification for common SKUs above")
