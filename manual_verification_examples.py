print("=== MANUAL VERIFICATION EXAMPLES ===")
print("Here are the step-by-step calculations you can check manually")
print("=" * 70)

# I'll create examples based on the data we can see
examples = [
    {
        'SKU': '7546978',
        'Plant': 'DE02',
        'Description': 'Seidel HB Pils 0,4 (6)',
        'PBI_Transactions': [
            {'Date': '08/07/2025', 'Qty': 1651.98, 'Value': 2436.62, 'Age_Months': 0.8},
            {'Date': '06/11/2024', 'Qty': 2823.27, 'Value': 4164.24, 'Age_Months': 14.7},
            {'Date': '12/15/2022', 'Qty': 9294.75, 'Value': 13709.47, 'Age_Months': 32.5}
        ],
        'PBI_Total_Stock': 13770.0,
        'PBI_Total_Value': 20310.33
    },
    {
        'SKU': '7546978',
        'Plant': 'DE30',
        'Description': '<PERSON><PERSON><PERSON> HB Pils 0,4 (6)',
        'PBI_Transactions': [
            {'Date': '11/15/2024', 'Qty': 4740.31, 'Value': 7858.98, 'Age_Months': 9.5},
            {'Date': '07/13/2023', 'Qty': 4052.71, 'Value': 6719.01, 'Age_Months': 25.6},
            {'Date': '05/04/2023', 'Qty': 9497.96, 'Value': 15746.72, 'Age_Months': 27.9},
            {'Date': '04/25/2023', 'Qty': 3967.02, 'Value': 6576.94, 'Age_Months': 28.2}
        ],
        'PBI_Total_Stock': 22258.0,
        'PBI_Total_Value': 36901.65
    }
]

for i, example in enumerate(examples, 1):
    print(f"\n[EXAMPLE {i}] SKU: {example['SKU']} | Plant: {example['Plant']}")
    print(f"Description: {example['Description']}")
    print("=" * 70)
    
    print(f"📊 POWER BI CALCULATION BREAKDOWN:")
    print(f"Individual Transactions:")
    
    total_qty_check = 0
    total_value_check = 0
    
    for j, trans in enumerate(example['PBI_Transactions'], 1):
        print(f"  [{j}] Date: {trans['Date']}")
        print(f"      Quantity: {trans['Qty']:,.2f}")
        print(f"      Value: {trans['Value']:,.2f}")
        print(f"      Age (months): {trans['Age_Months']}")
        print(f"      Status: {'Fast Mover' if trans['Age_Months'] < 36 else 'Slow Mover'}")
        
        total_qty_check += trans['Qty']
        total_value_check += trans['Value']
    
    print(f"\n🧮 MANUAL CALCULATION CHECK:")
    print(f"Sum of individual quantities: {total_qty_check:,.2f}")
    print(f"Power BI Total Stock: {example['PBI_Total_Stock']:,.2f}")
    print(f"Quantity Match: {'✅ YES' if abs(total_qty_check - example['PBI_Total_Stock']) < 0.01 else '❌ NO'}")
    
    print(f"\nSum of individual values: {total_value_check:,.2f}")
    print(f"Power BI Total Value: {example['PBI_Total_Value']:,.2f}")
    print(f"Value Match: {'✅ YES' if abs(total_value_check - example['PBI_Total_Value']) < 0.01 else '❌ NO'}")
    
    print(f"\n📋 FIFO INVENTORY LOGIC:")
    print(f"This represents the FIFO (First In, First Out) inventory calculation:")
    print(f"- Each transaction shows when inventory was received")
    print(f"- Older inventory (higher age) should be impaired first")
    print(f"- Total Stock = Sum of all individual transaction quantities")
    print(f"- Age determines impairment status and category")
    
    print(f"\n🔍 AGE-BASED IMPAIRMENT ANALYSIS:")
    for trans in example['PBI_Transactions']:
        if trans['Age_Months'] > 36:
            status = "IMPAIRED (>36 months)"
        elif trans['Age_Months'] > 24:
            status = "AT RISK (>24 months)"
        elif trans['Age_Months'] > 12:
            status = "MONITOR (>12 months)"
        else:
            status = "GOOD (<12 months)"
        
        print(f"  {trans['Date']}: {trans['Qty']:,.0f} units, {trans['Age_Months']:.1f} months → {status}")

print(f"\n" + "=" * 70)
print("🎯 HOW TO MANUALLY VERIFY ANY SKU-PLANT COMBINATION:")
print("=" * 70)

print(f"""
STEP 1: FIND THE SKU-PLANT IN POWER BI DATA
- Open: powerbi_upload_fixed_dates.csv
- Filter by SKU and Plant you want to check
- Note all individual transaction records

STEP 2: VERIFY QUANTITY CALCULATION
- Sum up all 'Assigned' values for that SKU-Plant
- This should equal the 'Total Stock' value
- Formula: Total Stock = Sum of all Assigned quantities

STEP 3: VERIFY VALUE CALCULATION  
- Sum up all 'Value Assigned' values for that SKU-Plant
- This should equal the 'Total Value' value
- Formula: Total Value = Sum of all Value Assigned amounts

STEP 4: VERIFY AGE CALCULATION
- Check 'Entry date' vs 'Today' (31st August 2025)
- Calculate months difference
- Verify 'Months' column matches your calculation

STEP 5: VERIFY IMPAIRMENT LOGIC
- Age < 12 months = Fast Mover
- Age 12-36 months = Medium Mover  
- Age > 36 months = Slow Mover
- Glass items have different thresholds

STEP 6: VERIFY FIFO EQUATION
- Opening Stock + Period Movements = Closing Stock
- Our data shows Closing Stock (as of 31st Aug 2025)
- Each transaction represents historical receipts
- FIFO means oldest inventory is impaired first
""")

print(f"\n📊 SAMPLE VERIFICATION CHECKLIST:")
print("For SKU 7546978, Plant DE02:")
print("□ Individual quantities sum to 13,770.00")
print("□ Individual values sum to 20,310.33") 
print("□ Oldest transaction (Dec 2022) is 32.5 months old")
print("□ All transactions marked as 'Fast Mover' (Glass category)")
print("□ Entry dates are properly formatted")
print("□ Age calculations are correct vs 31st August 2025")

print(f"\n💡 COMMON VERIFICATION ISSUES TO CHECK:")
print("- Date formatting (should show actual dates, not 00:00:00)")
print("- Age calculations (months between entry date and 31st Aug 2025)")
print("- Category assignment (Glass vs Others)")
print("- Status determination (Fast/Medium/Slow Mover)")
print("- FIFO sequence (oldest transactions first)")
print("- Aggregation accuracy (sum of parts = total)")

print(f"\n✅ IF ALL CHECKS PASS:")
print("Your Power BI data is mathematically correct and follows proper FIFO logic")
print("Any discrepancies with other reports likely come from:")
print("- Different time periods")
print("- Different business rules")
print("- Different data sources")
print("- Different aggregation methods")
