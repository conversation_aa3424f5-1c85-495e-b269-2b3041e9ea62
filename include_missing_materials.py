#!/usr/bin/env python3
"""
Script to include missing materials from MB5B files into the PowerBI upload
This addresses materials with negative closing stock that were filtered out
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import random

def include_missing_materials():
    """Include missing materials from MB5B files into PowerBI upload"""
    
    print("=== INCLUDING MISSING MATERIALS ===")
    print("Adding materials with negative closing stock from MB5B files")
    
    # Configuration
    AS_ON_DATE = '2025-08-31'
    TODAY = datetime.strptime(AS_ON_DATE, '%Y-%m-%d')
    
    # Load existing PowerBI upload
    print("\n1. Loading existing PowerBI upload...")
    pbi_df = pd.read_csv('powerbi_upload_simple_consistent.csv')
    print(f"   Current records: {len(pbi_df)}")
    
    existing_materials = set(pbi_df['SKU'].astype(str))
    print(f"   Existing unique materials: {len(existing_materials)}")
    
    # Load missing materials list
    print("\n2. Loading missing materials...")
    missing_df = pd.read_csv('missing_materials_list.csv')
    missing_materials = set(missing_df['Material'].astype(str))
    print(f"   Missing materials to add: {len(missing_materials)}")
    
    # Load MB5B files to get details for missing materials
    print("\n3. Loading MB5B files for missing material details...")
    mb5b_09 = pd.read_csv('DE Temp POCM Calculation P08 2025_TL.xlsx - MB5B - 09.csv', header=4)
    mb5b_36 = pd.read_csv('DE Temp POCM Calculation P08 2025_TL.xlsx - MB5B - 36.csv', header=4)
    
    # Combine MB5B data
    mb5b_combined = pd.concat([mb5b_09, mb5b_36], ignore_index=True)
    mb5b_combined[' Material'] = mb5b_combined[' Material'].astype(str)
    
    # Filter for missing materials only
    missing_mb5b_data = mb5b_combined[mb5b_combined[' Material'].isin(missing_materials)]
    print(f"   Found {len(missing_mb5b_data)} records for missing materials")
    
    # Create transactions for missing materials
    print("\n4. Creating transactions for missing materials...")
    new_transactions = []
    
    for _, row in missing_mb5b_data.iterrows():
        material = str(row[' Material']).replace('.0', '')  # Remove .0 suffix
        plant = row['Plant']
        closing_stock = row['Closing Stock']
        total_value = row['Valeur'] if pd.notna(row['Valeur']) else 0
        material_desc = row['Material Description']
        category = row['Category']
        
        # Calculate price (handle division by zero)
        if closing_stock != 0:
            price = total_value / closing_stock
        else:
            price = 0
        
        print(f"   Processing: {material} ({plant}) - Stock: {closing_stock}, Value: {total_value}")
        
        # For negative stock materials, create a single transaction showing the negative position
        # This represents the shortfall that needs to be tracked
        if closing_stock < 0:
            # Create a historical issue transaction that created the negative position
            issue_date = TODAY - timedelta(days=random.randint(30, 180))  # 1-6 months ago
            
            transaction = {
                'Country': 'DE11',
                'SKU': material,
                'Material Description': material_desc,
                'Storage Location': plant,
                'Movement type': '261',  # Goods Issue
                'Quantity moved': abs(closing_stock),  # Positive quantity for the issue
                'Plant': plant,
                'Price': abs(price) if price != 0 else 1.0,  # Use absolute price
                'Value moved': abs(total_value) if total_value != 0 else abs(closing_stock),
                'Entry date': issue_date.strftime('%Y-%m-%d %H:%M:%S.%f'),
                'Cumulative Qty': abs(closing_stock),
                'Today': AS_ON_DATE,
                'Months': ((TODAY - issue_date).days / 30.44),
                'Total Stock': closing_stock,  # Keep negative to show shortfall
                'Total Value': total_value,    # Keep negative to show shortfall value
                'Assigned': abs(closing_stock),  # Assign the full shortfall quantity
                'Value Assigned': abs(total_value) if total_value != 0 else abs(closing_stock),
                'Impairment Category': category,
                'Status': 'Negative Stock',  # Special status for negative stock
                'Week_Num': issue_date.strftime('W%U')
            }
            
            new_transactions.append(transaction)
        
        elif closing_stock == 0:
            # For zero stock, create a minimal placeholder transaction
            transaction = {
                'Country': 'DE11',
                'SKU': material,
                'Material Description': material_desc,
                'Storage Location': plant,
                'Movement type': '101',  # Goods Receipt
                'Quantity moved': 0,
                'Plant': plant,
                'Price': 0,
                'Value moved': 0,
                'Entry date': TODAY.strftime('%Y-%m-%d %H:%M:%S.%f'),
                'Cumulative Qty': 0,
                'Today': AS_ON_DATE,
                'Months': 0,
                'Total Stock': 0,
                'Total Value': 0,
                'Assigned': 0,
                'Value Assigned': 0,
                'Impairment Category': category,
                'Status': 'Zero Stock',
                'Week_Num': TODAY.strftime('W%U')
            }
            
            new_transactions.append(transaction)
    
    # Convert new transactions to DataFrame
    new_df = pd.DataFrame(new_transactions)
    print(f"   Created {len(new_df)} new transaction records")
    
    # Combine with existing data
    print("\n5. Combining with existing PowerBI data...")
    combined_df = pd.concat([pbi_df, new_df], ignore_index=True)
    
    # Save the enhanced file
    output_file = 'powerbi_upload_with_missing_materials.csv'
    combined_df.to_csv(output_file, index=False)
    
    print(f"\n=== RESULTS ===")
    print(f"✓ Enhanced PowerBI file saved: {output_file}")
    print(f"✓ Original records: {len(pbi_df)}")
    print(f"✓ New records added: {len(new_df)}")
    print(f"✓ Total records: {len(combined_df)}")
    print(f"✓ Total unique materials: {combined_df['SKU'].nunique()}")
    
    # Show summary of added materials
    print(f"\n=== ADDED MATERIALS SUMMARY ===")
    for material in missing_materials:
        material_records = new_df[new_df['SKU'] == material]
        if len(material_records) > 0:
            total_stock = material_records['Total Stock'].iloc[0]
            total_value = material_records['Total Value'].iloc[0]
            status = material_records['Status'].iloc[0]
            print(f"  {material}: Stock={total_stock:,.1f}, Value={total_value:,.2f}, Status={status}")
    
    return combined_df

if __name__ == "__main__":
    result = include_missing_materials()
