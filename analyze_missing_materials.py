#!/usr/bin/env python3
"""
Script to analyze missing materials/SKUs between MB5B files and PowerBI upload file
"""

import pandas as pd
import numpy as np

def analyze_missing_materials():
    """Analyze what materials are missing from the PowerBI upload file"""
    
    print("Loading data files...")
    
    # Read the three files with proper headers
    try:
        # MB5B files have headers starting at row 4 (0-indexed)
        mb5b_09 = pd.read_csv('DE Temp POCM Calculation P08 2025_TL.xlsx - MB5B - 09.csv', header=4)
        print(f"✓ Loaded MB5B 09 months: {len(mb5b_09)} rows")
        print(f"  Columns: {mb5b_09.columns.tolist()}")
    except Exception as e:
        print(f"✗ Error loading MB5B 09: {e}")
        return

    try:
        mb5b_36 = pd.read_csv('DE Temp POCM Calculation P08 2025_TL.xlsx - MB5B - 36.csv', header=4)
        print(f"✓ Loaded MB5B 36 months: {len(mb5b_36)} rows")
        print(f"  Columns: {mb5b_36.columns.tolist()}")
    except Exception as e:
        print(f"✗ Error loading MB5B 36: {e}")
        return

    try:
        pbi_upload = pd.read_csv('powerbi_upload_simple_consistent.csv')
        print(f"✓ Loaded PowerBI upload: {len(pbi_upload)} rows")
        print(f"  Columns: {pbi_upload.columns.tolist()}")
    except Exception as e:
        print(f"✗ Error loading PowerBI upload: {e}")
        return
    
    print("\n" + "="*60)
    print("MATERIAL ANALYSIS")
    print("="*60)
    
    # Get unique materials from each file
    # Find the material column (it might have different names)
    material_col_09 = None
    material_col_36 = None

    for col in mb5b_09.columns:
        if 'material' in col.lower() or 'Material' in col:
            material_col_09 = col
            break

    for col in mb5b_36.columns:
        if 'material' in col.lower() or 'Material' in col:
            material_col_36 = col
            break

    print(f"Material column in MB5B 09: '{material_col_09}'")
    print(f"Material column in MB5B 36: '{material_col_36}'")

    if material_col_09 is None or material_col_36 is None:
        print("Could not find material columns!")
        return

    mb5b_09_materials = set(mb5b_09[material_col_09].dropna().unique())
    mb5b_36_materials = set(mb5b_36[material_col_36].dropna().unique())
    pbi_materials = set(pbi_upload['SKU'].dropna().unique())
    
    print(f"Unique materials in MB5B 09 months: {len(mb5b_09_materials)}")
    print(f"Unique materials in MB5B 36 months: {len(mb5b_36_materials)}")
    print(f"Unique materials in PowerBI upload: {len(pbi_materials)}")
    
    # Find missing materials
    missing_from_09 = mb5b_09_materials - pbi_materials
    missing_from_36 = mb5b_36_materials - pbi_materials
    all_mb5b_materials = mb5b_09_materials.union(mb5b_36_materials)
    total_missing = all_mb5b_materials - pbi_materials
    
    print(f"\nMaterials in MB5B 09 but NOT in PowerBI upload: {len(missing_from_09)}")
    print(f"Materials in MB5B 36 but NOT in PowerBI upload: {len(missing_from_36)}")
    print(f"Total unique materials missing from PowerBI upload: {len(total_missing)}")
    
    # Analyze why materials might be missing
    print("\n" + "="*60)
    print("ANALYSIS OF MISSING MATERIALS")
    print("="*60)
    
    # Check materials with zero closing stock in MB5B files
    mb5b_09_zero_stock = mb5b_09[mb5b_09['Closing Stock'] == 0]
    mb5b_36_zero_stock = mb5b_36[mb5b_36['Closing Stock'] == 0]
    
    print(f"\nMB5B 09 - Materials with zero closing stock: {len(mb5b_09_zero_stock)}")
    print(f"MB5B 36 - Materials with zero closing stock: {len(mb5b_36_zero_stock)}")
    
    # Check if missing materials have zero stock
    missing_09_zero = mb5b_09[mb5b_09[material_col_09].isin(missing_from_09) & (mb5b_09['Closing Stock'] == 0)]
    missing_36_zero = mb5b_36[mb5b_36[material_col_36].isin(missing_from_36) & (mb5b_36['Closing Stock'] == 0)]
    
    print(f"\nMissing from 09 with zero closing stock: {len(missing_09_zero)}")
    print(f"Missing from 36 with zero closing stock: {len(missing_36_zero)}")
    
    # Show some examples of missing materials
    if len(missing_from_09) > 0:
        print(f"\nFirst 10 materials missing from MB5B 09:")
        for i, material in enumerate(list(missing_from_09)[:10]):
            material_data = mb5b_09[mb5b_09[material_col_09] == material].iloc[0]
            print(f"  {i+1}. {material} - Closing Stock: {material_data['Closing Stock']}")

    if len(missing_from_36) > 0:
        print(f"\nFirst 10 materials missing from MB5B 36:")
        for i, material in enumerate(list(missing_from_36)[:10]):
            material_data = mb5b_36[mb5b_36[material_col_36] == material].iloc[0]
            print(f"  {i+1}. {material} - Closing Stock: {material_data['Closing Stock']}")
    
    # Save missing materials to CSV for further analysis
    if len(total_missing) > 0:
        missing_materials_df = pd.DataFrame({'Material': list(total_missing)})
        missing_materials_df.to_csv('missing_materials_list.csv', index=False)
        print(f"\n✓ Saved {len(total_missing)} missing materials to 'missing_materials_list.csv'")
    
    return {
        'mb5b_09_materials': mb5b_09_materials,
        'mb5b_36_materials': mb5b_36_materials,
        'pbi_materials': pbi_materials,
        'missing_from_09': missing_from_09,
        'missing_from_36': missing_from_36,
        'total_missing': total_missing
    }

if __name__ == "__main__":
    results = analyze_missing_materials()
