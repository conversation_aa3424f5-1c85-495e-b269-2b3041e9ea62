import pandas as pd

print("=== CREATING VERIFICATION SPREADSHEET ===")

# Load the Power BI data
df = pd.read_csv('powerbi_upload_fixed_dates.csv')

print(f"Loaded {len(df)} records")

# Create a summary by SKU and Plant
summary = df.groupby(['SKU', 'Plant']).agg({
    'Material Description': 'first',
    'Total Stock': 'first',
    'Total Value': 'first',
    'Assigned': 'sum',
    'Value Assigned': 'sum',
    'Impairment Category': 'first',
    'Status': 'first'
}).reset_index()

# Add verification columns
summary['Qty_Check'] = summary['Total Stock'] - summary['Assigned']
summary['Value_Check'] = summary['Total Value'] - summary['Value Assigned']
summary['Qty_Match'] = abs(summary['Qty_Check']) < 0.01
summary['Value_Match'] = abs(summary['Value_Check']) < 0.01

# Create detailed transaction list
detailed = df[['SKU', 'Plant', 'Material Description', 'Entry date', 'Assigned', 'Value Assigned', 'Months', 'Status']].copy()
detailed = detailed.sort_values(['SKU', 'Plant', 'Entry date'])

# Save both files
summary.to_csv('verification_summary.csv', index=False)
detailed.to_csv('verification_detailed.csv', index=False)

print(f"✅ Created verification files:")
print(f"  - verification_summary.csv ({len(summary)} SKU-Plant combinations)")
print(f"  - verification_detailed.csv ({len(detailed)} individual transactions)")

# Show sample verification
print(f"\n📊 SAMPLE VERIFICATION:")
print("=" * 60)

for i, row in summary.head(5).iterrows():
    print(f"\nSKU {row['SKU']} | Plant {row['Plant']}")
    print(f"  Description: {row['Material Description']}")
    print(f"  Total Stock: {row['Total Stock']:,.2f}")
    print(f"  Sum of Assigned: {row['Assigned']:,.2f}")
    print(f"  Quantity Match: {'✅' if row['Qty_Match'] else '❌'}")
    print(f"  Total Value: {row['Total Value']:,.2f}")
    print(f"  Sum of Value Assigned: {row['Value Assigned']:,.2f}")
    print(f"  Value Match: {'✅' if row['Value_Match'] else '❌'}")

# Count matches
qty_matches = summary['Qty_Match'].sum()
value_matches = summary['Value_Match'].sum()
total_combinations = len(summary)

print(f"\n📋 VERIFICATION SUMMARY:")
print(f"Total SKU-Plant combinations: {total_combinations}")
print(f"Quantity matches: {qty_matches}/{total_combinations} ({qty_matches/total_combinations*100:.1f}%)")
print(f"Value matches: {value_matches}/{total_combinations} ({value_matches/total_combinations*100:.1f}%)")

if qty_matches == total_combinations and value_matches == total_combinations:
    print("✅ ALL CALCULATIONS ARE CORRECT!")
else:
    print("⚠️ Some discrepancies found - check the CSV files")

print(f"\n🎯 HOW TO USE THE VERIFICATION FILES:")
print("1. Open verification_summary.csv in Excel")
print("2. Check that Qty_Match and Value_Match columns are all TRUE")
print("3. For any FALSE values, investigate using verification_detailed.csv")
print("4. Filter verification_detailed.csv by SKU and Plant to see individual transactions")
print("5. Manually sum the 'Assigned' and 'Value Assigned' columns")
print("6. Compare with the totals in verification_summary.csv")
