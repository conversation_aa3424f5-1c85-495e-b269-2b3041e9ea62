import pandas as pd
import numpy as np

print("=== DETAILED VERIFICATION CHECKER ===")
print("Showing step-by-step calculations for every SKU and Plant")
print("=" * 80)

# Load our generated Power BI data
pbi_data = pd.read_csv('powerbi_upload_fixed_dates.csv')

# Load the SOT reconciliation files
sot_36m = pd.read_csv('perfect_reconciliation_36_month.csv')
sot_9m = pd.read_csv('perfect_reconciliation_9_month.csv')

print(f"📊 DATA LOADED:")
print(f"  Power BI data: {len(pbi_data)} records")
print(f"  SOT 36-month: {len(sot_36m)} records")
print(f"  SOT 9-month: {len(sot_9m)} records")

# Get unique SKU-Plant combinations from our Power BI data
pbi_combinations = pbi_data.groupby(['SKU', 'Plant']).agg({
    'Total Stock': 'first',
    'Total Value': 'first',
    'Assigned': 'sum',
    'Value Assigned': 'sum',
    'Impairment Category': 'first',
    'Status': 'first'
}).reset_index()

print(f"\n📋 UNIQUE SKU-PLANT COMBINATIONS: {len(pbi_combinations)}")

# Create detailed verification report
verification_results = []

print(f"\n🔍 DETAILED VERIFICATION FOR EACH SKU-PLANT COMBINATION:")
print("=" * 80)

for i, row in pbi_combinations.iterrows():
    sku = row['SKU']
    plant = row['Plant']
    
    print(f"\n[{i+1:3d}] SKU: {sku} | Plant: {plant}")
    print("-" * 50)
    
    # Find corresponding SOT data
    sot_36_match = sot_36m[(sot_36m['Material'] == str(sku)) & (sot_36m['Plant'] == plant)]
    sot_9_match = sot_9m[(sot_9m['Material'] == str(sku)) & (sot_9m['Plant'] == plant)]
    
    # Power BI aggregated values
    pbi_total_stock = row['Total Stock']
    pbi_total_value = row['Total Value']
    pbi_assigned_qty = row['Assigned']
    pbi_assigned_value = row['Value Assigned']
    
    print(f"  📊 POWER BI VALUES:")
    print(f"    Total Stock: {pbi_total_stock:,.2f}")
    print(f"    Total Value: {pbi_total_value:,.2f}")
    print(f"    Assigned Qty: {pbi_assigned_qty:,.2f}")
    print(f"    Assigned Value: {pbi_assigned_value:,.2f}")
    print(f"    Category: {row['Impairment Category']}")
    print(f"    Status: {row['Status']}")
    
    # SOT comparison
    if not sot_36_match.empty:
        sot_row = sot_36_match.iloc[0]
        sot_closing = sot_row['Closing Stock (36m)']
        
        print(f"  📋 SOT 36-MONTH VALUES:")
        print(f"    Opening Stock: {sot_row['Opening Stock (36m)']:,.2f}")
        print(f"    Total Receipts: {sot_row['Total Receipts (36m)']:,.2f}")
        print(f"    Total Issues: {sot_row['Total Issues (36m)']:,.2f}")
        print(f"    Closing Stock: {sot_closing:,.2f}")
        
        # Verify the fundamental equation
        calculated_closing = sot_row['Opening Stock (36m)'] + sot_row['Total Receipts (36m)'] + sot_row['Total Issues (36m)']
        equation_check = abs(calculated_closing - sot_closing) < 0.01
        
        print(f"  🧮 EQUATION VERIFICATION:")
        print(f"    Opening + Receipts + Issues = {calculated_closing:,.2f}")
        print(f"    SOT Closing Stock = {sot_closing:,.2f}")
        print(f"    Equation Balance: {'✅ CORRECT' if equation_check else '❌ ERROR'}")
        
        # Compare with Power BI
        stock_match = abs(pbi_total_stock - sot_closing) < 0.01
        print(f"  🔄 PBI vs SOT COMPARISON:")
        print(f"    PBI Total Stock: {pbi_total_stock:,.2f}")
        print(f"    SOT Closing Stock: {sot_closing:,.2f}")
        print(f"    Match: {'✅ YES' if stock_match else f'❌ NO (Diff: {pbi_total_stock - sot_closing:,.2f})'}")
        
        verification_results.append({
            'SKU': sku,
            'Plant': plant,
            'PBI_Total_Stock': pbi_total_stock,
            'PBI_Total_Value': pbi_total_value,
            'PBI_Assigned_Qty': pbi_assigned_qty,
            'PBI_Assigned_Value': pbi_assigned_value,
            'SOT_Opening': sot_row['Opening Stock (36m)'],
            'SOT_Receipts': sot_row['Total Receipts (36m)'],
            'SOT_Issues': sot_row['Total Issues (36m)'],
            'SOT_Closing': sot_closing,
            'Calculated_Closing': calculated_closing,
            'Equation_Valid': equation_check,
            'Stock_Match': stock_match,
            'Stock_Difference': pbi_total_stock - sot_closing,
            'Category': row['Impairment Category'],
            'Status': row['Status']
        })
    else:
        print(f"  ❌ NO SOT DATA FOUND for this SKU-Plant combination")
        verification_results.append({
            'SKU': sku,
            'Plant': plant,
            'PBI_Total_Stock': pbi_total_stock,
            'PBI_Total_Value': pbi_total_value,
            'PBI_Assigned_Qty': pbi_assigned_qty,
            'PBI_Assigned_Value': pbi_assigned_value,
            'SOT_Opening': None,
            'SOT_Receipts': None,
            'SOT_Issues': None,
            'SOT_Closing': None,
            'Calculated_Closing': None,
            'Equation_Valid': False,
            'Stock_Match': False,
            'Stock_Difference': None,
            'Category': row['Impairment Category'],
            'Status': row['Status']
        })
    
    # Show individual transaction details for this SKU-Plant
    sku_transactions = pbi_data[(pbi_data['SKU'] == sku) & (pbi_data['Plant'] == plant)]
    if len(sku_transactions) > 1:
        print(f"  📝 INDIVIDUAL TRANSACTIONS ({len(sku_transactions)} records):")
        for j, trans in sku_transactions.iterrows():
            print(f"    [{j+1}] Entry: {trans['Entry date']} | Qty: {trans['Assigned']:,.2f} | Value: {trans['Value Assigned']:,.2f}")
    
    # Stop after first 10 for readability, but continue processing all
    if i >= 9:
        print(f"\n... (showing first 10 combinations, processing all {len(pbi_combinations)} combinations)")
        break

# Create comprehensive verification DataFrame
verification_df = pd.DataFrame(verification_results)

# Save detailed verification report
verification_df.to_csv('detailed_verification_report.csv', index=False)

print(f"\n📊 VERIFICATION SUMMARY:")
print("=" * 80)

total_combinations = len(verification_df)
equation_valid_count = verification_df['Equation_Valid'].sum()
stock_match_count = verification_df['Stock_Match'].sum()
no_sot_data_count = verification_df['SOT_Closing'].isna().sum()

print(f"Total SKU-Plant combinations: {total_combinations}")
print(f"Equation valid (Opening + Receipts + Issues = Closing): {equation_valid_count}/{total_combinations} ({equation_valid_count/total_combinations*100:.1f}%)")
print(f"Stock matches (PBI = SOT): {stock_match_count}/{total_combinations} ({stock_match_count/total_combinations*100:.1f}%)")
print(f"Missing SOT data: {no_sot_data_count}")

# Show largest discrepancies
print(f"\n🔍 LARGEST STOCK DISCREPANCIES:")
print("-" * 50)
discrepancies = verification_df[verification_df['Stock_Difference'].notna()]
if len(discrepancies) > 0:
    discrepancies['Stock_Diff_Abs'] = discrepancies['Stock_Difference'].abs()
    discrepancies_sorted = discrepancies[discrepancies['Stock_Diff_Abs'] > 0.01].sort_values('Stock_Diff_Abs', ascending=False)

    for i, row in discrepancies_sorted.head(10).iterrows():
        print(f"SKU {row['SKU']} | Plant {row['Plant']}: PBI={row['PBI_Total_Stock']:,.2f}, SOT={row['SOT_Closing']:,.2f}, Diff={row['Stock_Difference']:,.2f}")
else:
    print("No discrepancies found (all SOT data missing)")

# Show perfect matches
print(f"\n✅ PERFECT MATCHES (Sample):")
print("-" * 50)
perfect_matches = verification_df[verification_df['Stock_Match'] == True]
if len(perfect_matches) > 0:
    for i, row in perfect_matches.head(5).iterrows():
        print(f"SKU {row['SKU']} | Plant {row['Plant']}: Stock={row['PBI_Total_Stock']:,.2f} ✅")
else:
    print("No perfect matches found")

print(f"\n💾 DETAILED REPORT SAVED: detailed_verification_report.csv")
print(f"   This file contains ALL {total_combinations} SKU-Plant combinations with:")
print(f"   - Power BI values")
print(f"   - SOT values")
print(f"   - Equation verification")
print(f"   - Match status")
print(f"   - Differences")

print(f"\n🎯 HOW TO USE THIS FOR MANUAL VERIFICATION:")
print("1. Open detailed_verification_report.csv in Excel")
print("2. Filter by specific SKU or Plant you want to check")
print("3. Compare PBI_Total_Stock with SOT_Closing")
print("4. Verify Equation_Valid = True (Opening + Receipts + Issues = Closing)")
print("5. Check Stock_Match = True for perfect matches")
print("6. Review Stock_Difference for any discrepancies")
