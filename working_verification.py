import pandas as pd

print("=== WORKING VERIFICATION CHECKER ===")
print("Showing calculations you can manually verify")
print("=" * 60)

# Load data
pbi_data = pd.read_csv('powerbi_upload_fixed_dates.csv')
sot_36m = pd.read_csv('perfect_reconciliation_36_month.csv')

print(f"📊 DATA LOADED:")
print(f"  Power BI data: {len(pbi_data)} records")
print(f"  SOT 36-month: {len(sot_36m)} records")

# Get unique SKUs
pbi_skus = set(pbi_data['SKU'].astype(str).unique())
sot_skus = set(sot_36m['Material'].astype(str).unique())

print(f"\n📋 SKU ANALYSIS:")
print(f"  Power BI unique SKUs: {len(pbi_skus)}")
print(f"  SOT unique SKUs: {len(sot_skus)}")

# Find common SKUs
common_skus = pbi_skus.intersection(sot_skus)
print(f"  Common SKUs: {len(common_skus)}")

print(f"\n🔍 SAMPLE SKUs:")
print(f"  Power BI: {sorted(list(pbi_skus))[:10]}")
print(f"  SOT: {sorted(list(sot_skus))[:10]}")
print(f"  Common: {sorted(list(common_skus))[:10]}")

if len(common_skus) > 0:
    print(f"\n✅ DETAILED VERIFICATION FOR COMMON SKUs:")
    print("=" * 60)
    
    verification_results = []
    
    for sku in sorted(list(common_skus))[:10]:  # First 10 common SKUs
        print(f"\n📊 SKU: {sku}")
        print("-" * 40)
        
        # Get Power BI data
        pbi_sku_data = pbi_data[pbi_data['SKU'].astype(str) == sku]
        
        # Get SOT data  
        sot_sku_data = sot_36m[sot_36m['Material'].astype(str) == sku]
        
        # Group by plant
        pbi_by_plant = pbi_sku_data.groupby('Plant').agg({
            'Total Stock': 'first',
            'Total Value': 'first', 
            'Assigned': 'sum',
            'Value Assigned': 'sum'
        }).reset_index()
        
        for _, pbi_row in pbi_by_plant.iterrows():
            plant = pbi_row['Plant']
            
            # Find matching SOT data
            sot_match = sot_sku_data[sot_sku_data['Plant'] == plant]
            
            print(f"  Plant: {plant}")
            
            if not sot_match.empty:
                sot_row = sot_match.iloc[0]
                
                # SOT calculations
                sot_opening = sot_row['Opening Stock (36m)']
                sot_receipts = sot_row['Total Receipts (36m)']
                sot_issues = sot_row['Total Issues (36m)']
                sot_closing = sot_row['Closing Stock (36m)']
                
                # Verify equation: Opening + Receipts + Issues = Closing
                calculated_closing = sot_opening + sot_receipts + sot_issues
                equation_valid = abs(calculated_closing - sot_closing) < 0.01
                
                # Power BI values
                pbi_total_stock = pbi_row['Total Stock']
                pbi_assigned = pbi_row['Assigned']
                
                # Compare
                stock_match = abs(pbi_total_stock - sot_closing) < 0.01
                
                print(f"    📋 SOT CALCULATION:")
                print(f"      Opening Stock: {sot_opening:,.2f}")
                print(f"      + Receipts: {sot_receipts:,.2f}")
                print(f"      + Issues: {sot_issues:,.2f}")
                print(f"      = Calculated: {calculated_closing:,.2f}")
                print(f"      SOT Closing: {sot_closing:,.2f}")
                print(f"      Equation Valid: {'✅ YES' if equation_valid else '❌ NO'}")
                
                print(f"    📊 POWER BI VALUES:")
                print(f"      Total Stock: {pbi_total_stock:,.2f}")
                print(f"      Assigned Qty: {pbi_assigned:,.2f}")
                
                print(f"    🔄 COMPARISON:")
                print(f"      Stock Match: {'✅ YES' if stock_match else f'❌ NO (Diff: {pbi_total_stock - sot_closing:,.2f})'}")
                
                # Show individual transactions
                plant_transactions = pbi_sku_data[pbi_sku_data['Plant'] == plant]
                print(f"    📝 PBI TRANSACTIONS ({len(plant_transactions)}):")
                for _, trans in plant_transactions.iterrows():
                    print(f"      {trans['Entry date']}: Qty={trans['Assigned']:,.2f}, Value={trans['Value Assigned']:,.2f}")
                
                # Store result
                verification_results.append({
                    'SKU': sku,
                    'Plant': plant,
                    'SOT_Opening': sot_opening,
                    'SOT_Receipts': sot_receipts,
                    'SOT_Issues': sot_issues,
                    'SOT_Closing': sot_closing,
                    'Calculated_Closing': calculated_closing,
                    'Equation_Valid': equation_valid,
                    'PBI_Total_Stock': pbi_total_stock,
                    'PBI_Assigned': pbi_assigned,
                    'Stock_Match': stock_match,
                    'Stock_Difference': pbi_total_stock - sot_closing
                })
                
            else:
                print(f"    ❌ No SOT data for Plant {plant}")
                print(f"    📊 PBI Values: Stock={pbi_row['Total Stock']:,.2f}, Assigned={pbi_row['Assigned']:,.2f}")
    
    # Summary
    if verification_results:
        results_df = pd.DataFrame(verification_results)
        
        print(f"\n📊 VERIFICATION SUMMARY:")
        print("=" * 60)
        
        total_checks = len(results_df)
        equation_valid_count = results_df['Equation_Valid'].sum()
        stock_match_count = results_df['Stock_Match'].sum()
        
        print(f"Total SKU-Plant combinations checked: {total_checks}")
        print(f"SOT equations valid: {equation_valid_count}/{total_checks} ({equation_valid_count/total_checks*100:.1f}%)")
        print(f"Stock matches: {stock_match_count}/{total_checks} ({stock_match_count/total_checks*100:.1f}%)")
        
        # Show perfect matches
        perfect_matches = results_df[results_df['Stock_Match'] == True]
        if len(perfect_matches) > 0:
            print(f"\n✅ PERFECT MATCHES:")
            for _, row in perfect_matches.iterrows():
                print(f"  SKU {row['SKU']} | Plant {row['Plant']}: {row['PBI_Total_Stock']:,.2f} ✅")
        
        # Show discrepancies
        discrepancies = results_df[results_df['Stock_Match'] == False]
        if len(discrepancies) > 0:
            print(f"\n❌ DISCREPANCIES:")
            for _, row in discrepancies.iterrows():
                print(f"  SKU {row['SKU']} | Plant {row['Plant']}: PBI={row['PBI_Total_Stock']:,.2f}, SOT={row['SOT_Closing']:,.2f}, Diff={row['Stock_Difference']:,.2f}")
        
        # Save results
        results_df.to_csv('manual_verification_results.csv', index=False)
        print(f"\n💾 Results saved to: manual_verification_results.csv")

else:
    print(f"\n❌ NO COMMON SKUs FOUND!")
    print("Power BI and SOT data have completely different SKUs")
    
    print(f"\n🔍 INVESTIGATING THE MISMATCH:")
    
    # Check a few specific SKUs manually
    test_skus = ['7546978', '7546979', '7546980']
    
    for sku in test_skus:
        print(f"\nTesting SKU {sku}:")
        
        # Check in Power BI
        pbi_match = pbi_data[pbi_data['SKU'].astype(str) == sku]
        print(f"  In Power BI: {'YES' if not pbi_match.empty else 'NO'} ({len(pbi_match)} records)")
        
        # Check in SOT
        sot_match = sot_36m[sot_36m['Material'].astype(str) == sku]
        print(f"  In SOT: {'YES' if not sot_match.empty else 'NO'} ({len(sot_match)} records)")
        
        if not pbi_match.empty:
            print(f"    PBI Plants: {sorted(pbi_match['Plant'].unique())}")
            print(f"    PBI Total Stock: {pbi_match['Total Stock'].sum():,.2f}")
        
        if not sot_match.empty:
            print(f"    SOT Plants: {sorted(sot_match['Plant'].unique())}")
            print(f"    SOT Closing Stock: {sot_match['Closing Stock (36m)'].sum():,.2f}")

print(f"\n🎯 HOW TO USE THIS FOR MANUAL VERIFICATION:")
print("1. Look at the detailed calculations above")
print("2. For each SKU-Plant combination, verify:")
print("   - SOT equation: Opening + Receipts + Issues = Closing")
print("   - Power BI Total Stock should equal SOT Closing Stock")
print("3. Check individual transaction details")
print("4. Use manual_verification_results.csv for systematic checking")
