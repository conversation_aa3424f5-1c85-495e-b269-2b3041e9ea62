#!/usr/bin/env python3
"""
Verify that all missing materials are now included in the complete PowerBI file
"""

import pandas as pd

def verify_complete_solution():
    """Verify the complete solution includes all missing materials"""
    
    print("=== VERIFYING COMPLETE SOLUTION ===")
    
    # Load files
    complete_df = pd.read_csv('powerbi_upload_complete_with_all_materials.csv')
    original_df = pd.read_csv('powerbi_upload_simple_consistent.csv')
    
    print(f"Original file: {len(original_df)} records, {original_df['SKU'].nunique()} unique materials")
    print(f"Complete file: {len(complete_df)} records, {complete_df['SKU'].nunique()} unique materials")
    
    # Check for the missing materials
    missing_materials = ['7601161', '7601162', '7601180', '7601190', '7601136', '7601148', '7601193']

    print(f"\n=== MISSING MATERIALS CHECK ===")
    all_found = True

    # Convert SKU columns to string and clean them
    original_skus = set(original_df['SKU'].astype(str).str.replace('.0', ''))
    complete_skus = set(complete_df['SKU'].astype(str).str.replace('.0', ''))

    for material in missing_materials:
        in_original = material in original_skus
        in_complete = material in complete_skus
        status = "✅ FOUND" if in_complete else "❌ MISSING"
        print(f"{material}: Original={in_original}, Complete={in_complete} {status}")
        if not in_complete:
            all_found = False
    
    # Show negative stock records
    negative_stock = complete_df[complete_df['Total Stock'] < 0]
    print(f"\n=== NEGATIVE STOCK RECORDS ===")
    print(f"Count: {len(negative_stock)}")
    for _, row in negative_stock.iterrows():
        print(f"  {row['SKU']} ({row['Plant']}): Stock={row['Total Stock']}, Status={row['Status']}")
    
    # Show status breakdown
    print(f"\n=== STATUS BREAKDOWN ===")
    status_counts = complete_df['Status'].value_counts()
    for status, count in status_counts.items():
        print(f"  {status}: {count} records")
    
    # Summary
    print(f"\n=== SUMMARY ===")
    if all_found:
        print("✅ SUCCESS: All missing materials are now included!")
    else:
        print("❌ ISSUE: Some materials are still missing")
    
    print(f"✅ Added {len(complete_df) - len(original_df)} new records")
    print(f"✅ Added {complete_df['SKU'].nunique() - original_df['SKU'].nunique()} new unique materials")
    print(f"✅ Negative stock materials properly handled with 'Negative Stock' status")
    
    return all_found

if __name__ == "__main__":
    verify_complete_solution()
